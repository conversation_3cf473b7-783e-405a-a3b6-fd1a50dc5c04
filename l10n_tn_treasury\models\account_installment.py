from odoo import models, fields, api, _
import time
from datetime import datetime
from odoo.exceptions import UserError
from .amount_to_text_fr import amount_to_text_fr
import logging
from odoo.tools.translate import _

_logger = logging.getLogger(__name__)


class AccountInstallment(models.Model):

    _name = 'account.installment'
    _description = 'Installment'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']

    num_versement = fields.Char('Numéro de versement',store=True,required=True)
    coffre = fields.Boolean('Coffre', default=False, store=True)
    name = fields.Char('Reference', copy=False, readonly=True, select=True)
    date_vesement = fields.Date(string='Vesement Date', default=datetime.now().strftime('%Y-%m-%d'),
                                readonly=True, states={'draft': [('readonly', False)]}, copy=False)
    date_from = fields.Date(string='Start Date', default=datetime.now().strftime('%Y-%m-%d'),
                            readonly=True, states={'draft': [('readonly', False)]})
    date_to = fields.Date(string='End Date', default=datetime.now().strftime('%Y-%m-%d'),
                          readonly=True, states={'draft': [('readonly', False)]})
    bank_target = fields.Many2one('res.partner.bank', 'Target Bank', readonly=True,
                                  states={'draft': [('readonly', False)]},
                                  domain=[('company_id', '<>', False)])
    journal_id = fields.Many2one('account.journal', 'Journal', readonly=True, states={'draft': [('readonly', False)]},
                                 domain=[('type', '=', 'bank')])
    treasury_ids = fields.Many2many('account.treasury', 'account_vesement_treasury_rel', 'vesement_id', 'treasury_id',
                                    'Associated Document', domain="[('type_transaction', '=', 'receipt'), ('journal_id', '=', journal_id.id), ('payment_method_line_id', '=', 'check_printing')]",
                                    readonly=True, states={'draft': [('readonly', False)]})
    amount = fields.Float(string='Total', digits='Product Price', readonly=True, compute='_compute_amount')
    amount_in_word = fields.Char("Amount in Word")
    company_id = fields.Many2one('res.company', 'Company', default=lambda self: self.env.company)
    move_id = fields.Many2one('account.move', 'Account Entry', copy=False)
    move_ids = fields.One2many(related='move_id.line_ids', relation='account.move.line', string='Journal Items',
                               readonly=True)
    number = fields.Char('Number', required=1, readonly=True, states={'draft': [('readonly', False)]},
                         compute="_compute_treasury_number")
    note = fields.Text('Notes')
    state = fields.Selection([
        ('draft', 'Open'),
        ('valid', 'Validate'),
        ('cancel', 'Cancel'),
    ], 'State', required=True, readonly=True, select=1, default='draft', tracking=True)

    @api.depends('treasury_ids')
    def _compute_treasury_number(self):
        for rec in self:
            rec.number = len(rec.treasury_ids)

    def _compute_amount(self):
        for rec in self:
            rec.amount = sum(line.amount for line in rec.treasury_ids)

    def button_draft(self):
        self.state = 'draft'

    def action_move_line_create(self):
        for vesement in self:
            if vesement.move_id:
                continue
            # Create the account move record.
            move = {
                'journal_id': vesement.journal_id.id,
                'date': vesement.date_vesement,
                'ref': vesement.name,
                'move_type': 'entry',
                'line_ids': [],
            }
            for line in vesement.treasury_ids:
                debit = {
                    'name': "CHEQUE[" + line.holder.name + "]N:[" + line.name + "]DV:" + str(
                        line.maturity_date) or '/' + line.ref,
                    'partner_id': line.holder.id,
                    'debit': line.amount,
                    'credit': 0,
                    'account_id': vesement.journal_id.suspense_account_id.id,
                    'date': vesement.date_vesement,
                    'date_maturity': line.maturity_date,
                    'payment_method_line_id': line.payment_method_line_id.id,
                    'num_piece': line.name,
                    'payment_id': line.payment_id.id,
                    'ref':line.ref,
                }
                move['line_ids'].append([0, False, debit])

            for line in vesement.treasury_ids:
                credit = {
                    'name': "CHEQUE[" + line.holder.name + "]N:[" + line.name + "]DV:" + str(
                        line.maturity_date) or '/',
                    'debit': 0,
                    'credit': line.amount,
                    'partner_id': line.holder.id,
                    'account_id': line.journal_id.suspense_account_id.id,
                    'date': vesement.date_vesement,
                    'date_maturity': line.maturity_date,
                    'payment_method_line_id': line.payment_method_line_id.id,
                    'num_piece': line.name,
                    'payment_id': line.payment_id.id,
                    'ref':line.ref,
                }
                move['line_ids'].append([0, False, credit])

            self.move_id = self.env['account.move'].create(move)
            self.move_id.action_post()
            for line in vesement.treasury_ids:
                
                if line.payment_type == 'inbound':
                    move_line = self.move_id.line_ids.filtered(
                        lambda l: (l.debit == line.amount) 
                                and (l.partner_id.id == line.holder.id) and (l.date_maturity==line.payment_id.maturity_date)
                    )
                    move_line2 = self.move_id.line_ids.filtered(
                        lambda l: (l.credit == line.amount) 
                                and (l.partner_id.id == line.holder.id) and (l.date_maturity==line.payment_id.maturity_date)
                    )
                elif line.payment_type == 'outbound':
                    move_line = self.move_id.line_ids.filtered(
                        lambda l: (l.credit == line.amount) 
                                and l.partner_id.id == line.holder.id and (l.date_maturity==line.payment_id.maturity_date)
                    )
                    move_line2 = self.move_id.line_ids.filtered(
                        lambda l: (l.debit == line.amount) 
                                and l.partner_id.id == line.holder.id and (l.date_maturity==line.payment_id.maturity_date)
                    )
                
                move_line2.write({
                    'payment_method_line_id': line.payment_method_line_id.id,
                    'num_piece': line.name,
                })
                move_line.write({
                    'payment_method_line_id': line.payment_method_line_id.id,
                    'num_piece': line.name,
                })
                
                if line.payment_id and not line.installment_move_line_id:
                    payment = self.env['account.payment'].browse(line.payment_id.id)
                    if payment:
                        _logger.info("payment %s", payment)
                        # Find move lines to reconcile
                        lines_to_reconcile = payment.move_id.line_ids.filtered(
                            lambda l: l.account_id == payment.outstanding_account_id and not l.reconciled
                        )
                        line.write({
                            'reconciled_move_line_ids': [(6, 0, lines_to_reconcile.ids)],
                            'installment_move_line_id': move_line2.id,
                        })
                        _logger.info("line %s", line)
                        lines_to_reconcile.write({
                            'reconciled': True,
                        })
                else:
                    (line.move_line_id + move_line2).reconcile()
 
                if len(move_line) > 1:
                    _logger.warning(
                        f"Multiple matching move lines found for amount: {line.amount}, partner: {line.holder.id}")
                    # Select the correct move line based on additional criteria
                    move_line = move_line[0]
                if move_line:
                    line.move_line_id = move_line.id
                    
                else:
                    _logger.warning(f"No matching move line found for amount: {line.amount}, partner: {line.holder.id}")

    def button_validate(self):
        _logger.info("ttttta1")
        if len(self.treasury_ids) == 0:
            raise UserError(_('no treasury line !'))
        _logger.info("ttttta2")
        for treasury in self.treasury_ids:
            if treasury.state not in ['in_cash', 'coffre']:
                raise UserError(_('Document number %s est déjà versé') % (
                    treasury.name))
            if self.journal_id.coffre:
                treasury.state = 'coffre'
            else:
                treasury.state = 'versed'
            treasury.bank_target = self.bank_target.id
        self.state = 'valid'
        self.action_move_line_create()
        self.amount_in_word = amount_to_text_fr(self.amount, currency='Dinars')
        for treasury in self.treasury_ids:
            if self.journal_id.coffre:
                treasury.journal_id = self.journal_id.id

    def button_cancel(self):
        for treasury in self.treasury_ids:
            if treasury.state not in ['versed', 'coffre']:
                raise UserError(_('Document number %s n est pas encours !') % (
                    treasury.name))
            # Restore payment state if exists
            if treasury.payment_id:
                payment = self.env['account.payment'].browse(treasury.payment_id.id)
                if payment:
                    # Find reconciled move lines
                    reconciled_lines = payment.move_id.line_ids.filtered(
                        lambda l: l.account_id == payment.outstanding_account_id and l.reconciled
                    )
                    
                    # Remove reconciliation
                    if reconciled_lines:
                        reconciled_lines.remove_move_reconcile()
                    
                    # Reset payment state
                    reconciled_lines.write({
                        'reconciled': False,
                    })
           # Reset treasury state
            if treasury.state == 'versed' and treasury.installment_move_line_id.journal_id.coffre:
                _logger.info("1")
                treasury.state = 'coffre'
                treasury.bank_target = treasury.journal_id.bank_account_id
                coffs=self.env['account.move.line'].search([('journal_id.coffre', '=', True)])
                _logger.info("coffs %s", coffs)
                debit_move_line=self.env['account.move.line'].search([('payment_id', '=', treasury.payment_id.id), ('journal_id.coffre', '=', True),('debit','>',0)])
                _logger.info("debit_move_line %s", debit_move_line)
                debit_move_line.write({
                    'reconciled': False,
                })
                if not debit_move_line:
                    raise UserError(_('No debit move line found for payment %s') % (treasury.payment_id.id))
                treasury.move_line_id = debit_move_line.id

            elif treasury.state == 'versed':
                _logger.info("2")
                treasury.installment_move_line_id = False
                treasury.state = 'in_cash'
                treasury.bank_target = False
                treasury.move_line_id = False
            elif treasury.journal_id.coffre:
                _logger.info("3")
                treasury.state = 'in_cash'
                treasury.bank_target = False
                treasury.installment_move_line_id = False
                treasury.journal_id=treasury.payment_id.journal_id
                treasury.move_line_id = False
        # Cancel and delete the move
        if self.move_id:
            self.move_id.button_draft()  # First set to draft before canceling
            self.move_id.button_cancel()
            self.move_id.with_context(force_delete=True).unlink()
        self.move_id = False
        self.state = 'cancel'


    @api.model
    def create(self, vals):
        vals['name'] = self.env['ir.sequence'].next_by_code('account.installment') or 'New'
        new_id = super(AccountInstallment, self).create(vals)

        try:
            # Post only a silent internal note (no email, no error)
            new_id.message_post(
                body=_("Vesement created"),
                message_type='comment',
                subtype_xmlid='mail.mt_note'
            )
        except Exception:
            # Fail silently — prevents crash or log noise
            _logger = logging.getLogger(__name__)
            _logger.warning("Message post failed, possibly due to email misconfiguration")

        return new_id

    def unlink(self):
        for vesement in self:
            if vesement.state != 'draft':
                raise UserError(_('You cannot delete this vesement !'))
        return super(AccountInstallment, self).unlink()

    @api.onchange('date_from', 'date_to')
    def onchange_date(self):
        if not self.date_from or not self.date_to:
            self.treasury_ids = []
        else:
            inv = self.env['account.treasury'].search([('state', '=', 'in_cash'),
                                                       ('maturity_date', '>=', self.date_from),
                                                     # ('payment_type', '=', 'inbound'),
                                                       ('maturity_date', '<=', self.date_to),
                                                       ('journal_id.incash_check', '=', True)])

            # ('type_transaction', '=', 'receipt')

            self.treasury_ids = [(6, 0, [x.id for x in inv])]

    @api.onchange('bank_target')
    def onchange_bank(self):
        self.journal_id = self.bank_target and self.bank_target.journal_id.id or False