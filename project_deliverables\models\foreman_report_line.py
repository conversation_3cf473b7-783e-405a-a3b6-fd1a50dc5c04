from odoo import models, fields, api

class ForemanReportLine(models.Model):
    _name = "foreman.report.line"
    _description = "Temporary report for delivered & consumed quantities"

    product_id = fields.Many2one('product.product', string='Produit', required=True)
    delivered_qty = fields.Float(string='Livré')
    consumed_qty = fields.Float(string='Consommé')
    report_id = fields.Many2one('foreman.report', string='Report', required=True)
