
from odoo import models, fields, api
import logging
_logger = logging.getLogger(__name__)
class ForemanReport(models.Model):
    _name = "foreman.report"
    _description = "Foreman report for dynamic computation"

    name = fields.Char(string="Nom", compute="_compute_name", store=True)
    site_foreman_id = fields.Many2one('res.users', string='Chef <PERSON>', required=True)
    line_ids = fields.One2many('foreman.report.line', 'site_foreman_id', string='Produits')

    @api.depends('site_foreman_id')
    def _compute_name(self):
        for rec in self:
            rec.name = rec.site_foreman_id.name

    @api.model
    def recompute_report(self):
        """Delete old lines and recompute"""
        self.env['foreman.report.line'].sudo().search([]).unlink()
        self.env['foreman.report'].sudo().search([]).unlink()
        _logger.info("Recompute report")
        # Gather all foremen from the project's team (crm.team has site_foreman_id)
        foreman_users = self.env['crm.team'].search([]).mapped('site_foreman_id')
        _logger.info("len Foreman users : %s" % len(foreman_users))
        for foreman in foreman_users:
            # Compute delivered qty
            delivered_lines = self.env['stock.move.line'].search([
                ('move_id.sale_line_id.project_id.team_id.site_foreman_id', '=', foreman.id),
                ('move_id.state', '=', 'done'),
                ('product_id', '!=', False),
            ])
            _logger.info("len Delivered lines : %s" % len(delivered_lines))
            delivered_grouped = {}
            for line in delivered_lines:
                if not line.product_id:
                    continue
                delivered_grouped.setdefault(line.product_id.id, 0)
                delivered_grouped[line.product_id.id] += line.qty_done

            # Compute consumed qty
            consumed_lines = self.env['project.comsumption.line'].search([
                ('project_id.team_id.site_foreman_id', '=', foreman.id),
                ('product_id', '!=', False),
            ])
            _logger.info("len Consumed lines : %s" % len(consumed_lines))
            consumed_grouped = {}
            for line in consumed_lines:
                if not line.product_id:
                    continue
                consumed_grouped.setdefault(line.product_id.id, 0)
                consumed_grouped[line.product_id.id] += line.product_uom_qty

            # Merge product ids
            product_ids = set(list(delivered_grouped.keys()) + list(consumed_grouped.keys()))
            _logger.info("len Product ids : %s" % len(product_ids))
            report = self.env['foreman.report'].sudo().create({
                'site_foreman_id': foreman.id,
                'line_ids': [
                    (0, 0, {
                        'product_id': pid,
                        'delivered_qty': delivered_grouped.get(pid, 0),
                        'consumed_qty': consumed_grouped.get(pid, 0),
                    })
                    for pid in product_ids if pid
                ]
            })

    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None, **read_kwargs):
        _logger.info("Search read")
        self.recompute_report()  # refresh report
        return super().search_read(domain, fields, offset, limit, order, **read_kwargs)
